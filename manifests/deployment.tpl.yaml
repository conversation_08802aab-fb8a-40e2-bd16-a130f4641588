apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
  namespace: ${NAMESPACE}
spec:
  replicas: 1
  serviceName: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
  selector:
    matchLabels:
      app: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
  template:
    metadata:
      labels:
        app: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
    spec:
      containers:
        - name: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
          image: 133841643524.dkr.ecr.us-east-2.amazonaws.com/${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}
          env:
            - name: BRANCH
              value: ${CI_COMMIT_BRANCH}
            - name: ENV
              value: ${ENV}
            - name: SERVER_ADDR
              value: ${SERVER_ADDR}
            - name: STATIC_PATH
              value: /data/uploader
          ports:
            - containerPort: 80
          volumeMounts:
            - name: static-volume
              mountPath: /data
      imagePullSecrets:
        - name: default-aws-ecr-us-east-2
  volumeClaimTemplates:
  - metadata:
      name: static-volume
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 100Gi