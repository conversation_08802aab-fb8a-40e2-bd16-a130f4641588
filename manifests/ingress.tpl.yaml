apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
  namespace: ${NAMESPACE}
  annotations:
    traefik.frontend.rule.type: PathPrefixStrip
spec:
  rules:
    - host: ${HOST}
      http:
        paths:
          - path: /${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
            backend:
              serviceName: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
              servicePort: http