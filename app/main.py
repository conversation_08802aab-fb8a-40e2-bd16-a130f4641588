# coding=utf-8
import datetime
import hashlib
import os
import re
import shutil
import mimetypes
import magic
import random

from flask import Flask, jsonify, request, send_from_directory, make_response
from urllib.parse import quote
from flask_cors import CORS

app = Flask(__name__)
CORS(app, send_wildcard=True)

TMP_DIR = '/tmp'
SERVER_ADDR = os.environ.get('SERVER_ADDR') or 'SERVER_ADDR'
STATIC_PATH = os.environ.get('STATIC_PATH') or os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


def md5_file(file_path):
    md5_obj = hashlib.md5()
    with open(file_path, 'rb') as file_obj:
        md5_obj.update(file_obj.read())
    file_md5_id = md5_obj.hexdigest()
    return file_md5_id


def get_upload_dir(doc):
    upload_dir = os.path.join(STATIC_PATH, doc, datetime.datetime.now().strftime('%Y-%m'))
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
    return upload_dir


def resolve_access_url(path):
    return os.path.join(SERVER_ADDR, 'download', path[len(STATIC_PATH) + 1:])

# 获取的文件的扩展名文件类型和文件内容中读取的文件类型
def get_file_type(file_path):
    # 从文件扩展名获取文件类型
    mime_type, _ = mimetypes.guess_type(file_path)
    file_magic = magic.Magic(mime=True)
    # 读取文件内容获取文件类型
    file_type = file_magic.from_file(file_path)

    return mime_type, file_type


@app.route('/download/<path:path>', methods=['GET'])
def download(path):
    file_name = request.args.get('rename') or os.path.basename(path)
    response = make_response(send_from_directory(
        STATIC_PATH,
        path,
        as_attachment=True,
        attachment_filename=file_name
    ))
    # print(path)
    # print(file_name)

    # the filename part in default header will ignore Chinese characaters
    # e.g. if the filename is "答复 Capvision Requested Consultation.msg"
    # the default header is: "attachment; filename=" Capvision Requested Consultation.msg"; filename*=UTF-8''%E7%AD%94%E5%A4%8D%20Capvision%20Requested%20Consultation.msg"
    # Note that the "答复" is ignored, so we may want to overwrite the header as follows
    # Anyway, for client doesn't support RFC5987, it may cause other problems.
    # (e.g. client may not url decode the filename, so the use may see "%E7%AD%94%E5%A4%8D%20Capvision%20Requested%20Consultation.msg")
    # response.headers["Content-Disposition"] = "attachment; filename=\"{0}\"; filename*=utf-8''{0}".format(quote(file_name))
    return response


@app.route('/upload/<doc>', methods=['GET'])
def test(doc):
    return '''
        <!doctype html>
        <title>Upload new File</title>
        <h1>Upload new File</h1>
        <form method=post enctype=multipart/form-data>
        <input type=file name=file>
        <input name=filename>
        <input type=submit value=Upload>
        </form>
    '''


@app.route('/upload/<doc>', methods=['POST'])
def upload(doc=None):
    if 'file' not in request.files:
        return jsonify({
            'code': '0202',
            'description': 'No file part'
        })

    request_filename = request.form.get("filename")
    file = request.files['file']
    filename = file.filename

    # save tmp file & md5 filename
    tmp_path = os.path.join(TMP_DIR, filename)
    file.save(tmp_path)

    # check file type
    # 特例判断：允许特定 file_type 和 mime_type 的组合
    allowed_exceptions = [
        {"file_type": "video/mp4", "mime_type": "audio/mpeg"}
    ]
    mime_type, file_type = get_file_type(tmp_path)
    # 检查扩展名对应的MIME类型和实际文件类型不一致不允许上传文件
    if mime_type != file_type:
        is_exception = any(
            exception["file_type"] == file_type and exception["mime_type"] == mime_type
            for exception in allowed_exceptions
        )
        if not is_exception:
            os.remove(tmp_path)
            return jsonify({
                'code': '0501',
                'msg': 'file type not match',
                'data': {
                    'file_name': filename,
                    'mime_type': mime_type,
                    'file_type': file_type
                }
            })

    md5_filename = (md5_file(tmp_path) + str(random.randint(0, 100))
                    + ''.join(re.findall(r'\.[^.]+$', filename)))

    # move to des path
    des_path = os.path.join(get_upload_dir(doc), request_filename or md5_filename)
    # 如果文件已经存在，拼接随机数防止文件被替换
    if os.path.exists(des_path):
        index = des_path.rfind('.')
        des_path = des_path[:index] + '_' + str(hex(random.randint(0, 100))) + des_path[index:]

    shutil.move(tmp_path, des_path)

    return jsonify({
        'code': '0000',
        'msg': 'ok',
        'data': {
            'file_name': filename,
            'url': resolve_access_url(des_path),
            'mime_type': mime_type,
            'file_type': file_type
        }
    })


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5005, debug=True)
    # print(check_file_type(f'/Users/<USER>/Downloads/test12.jpg'))
