# Microsoft Word 2007+ docx, 分三步查询确定是 docx 类型文件，最后给出 docx 文件的 mime type, 第二步和第三步之所以不用 string 而用 search 是因为 string 是 offset 的位置startwith， search 才是 substring
0   string      PK\x03\x04
>0  search/512  \x00[Content_Types].xml
>>0 search      \x00word/document.xml   Microsoft Word 2007+
!:mime application/vnd.openxmlformats-officedocument.wordprocessingml.document

# docx  Microsoft Excel 2007+，分三步查询确定是 xlsx 类型文件，最后给出 xlsx 文件的 mime type
0   string      PK\x03\x04
>0  search/512  \x00[Content_Types].xml
>>0 search      \x00xl/workbook.xml Microsoft Excel 2007+
!:mime application/vnd.openxmlformats-officedocument.spreadsheetml.sheet