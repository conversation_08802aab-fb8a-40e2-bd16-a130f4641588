workflow:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^.*aws$/ || $CI_COMMIT_BRANCH =~ /^.*preview$/
      when: always
    - when: never

cache: &global_cache
  key: ${CI_PROJECT_NAME}
  paths:
    - gradle_cache
  policy: pull-push

stages:
  - build


deploy_dev:
  extends: .build_scripts
  except:
    - /^release*$/
  environment:
    name: aws/${CI_COMMIT_BRANCH}
deploy_master:
  extends: .build_scripts
  only:
    - /^release*$/
  environment:
    name: aws/${CI_COMMIT_BRANCH}


.build_scripts:
  tags:
    - k8s
  stage: build
  image:
    name: 133841643524.dkr.ecr.us-east-2.amazonaws.com/kaniko_kubectl:v1.5
  before_script:
    - /kaniko/kubectl get secrets/default-aws-ecr-us-east-2  -n gitlab -o jsonpath='{.data.\.dockerconfigjson}' | base64 -d > /kaniko/.docker/config.json
    - export NAMESPACE=default
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ .*sg-.*$ ]]
      then
          export REGION=sg
          export NAMESPACE=sg
          export SERVER_ADDR=https://api-db3.capvision.com/uploader
          export MODULE=uploader-sg
      else
          export REGION=us
          export NAMESPACE=default
          export SERVER_ADDR=https://api-db2.capvision.com/uploader
          export MODULE=uploader
      fi
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ ^.*aws$ ]]
            then
                export ENV=default-${REGION}
                export HOST=${PROD_API_HOST}.${REGION}
            else
                export ENV=dev-${REGION}
                export NAMESPACE=dev
                export HOST=${DEV_API_HOST}.${REGION}
                export SERVER_ADDR=https://qa-udb2.capvision.com/uploader
            fi
    - export REPLICAS=1
  script:
    - |
      /kaniko/executor \
        --context "${CI_PROJECT_DIR}" \
        --build-arg BUILD_ENV=${CI_COMMIT_BRANCH} \
        --dockerfile "${CI_PROJECT_DIR}/Dockerfile" \
        --destination "133841643524.dkr.ecr.us-east-2.amazonaws.com/${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"

    - cd ${CI_PROJECT_DIR}/manifests/
    - export LC_ALL=C
    - /kaniko/envsubst < deployment.tpl.yaml > deployment.yaml
    - /kaniko/envsubst < ingress.tpl.yaml > ingress.yaml
    - /kaniko/envsubst < service.tpl.yaml > service.yaml
    - |
      /kaniko/kubectl apply -f deployment.yaml -f service.yaml -f ingress.yaml
  cache:
    <<: *global_cache
