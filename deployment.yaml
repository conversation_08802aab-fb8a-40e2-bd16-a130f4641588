apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: <JOB_NAME>-<BRANCH>
spec:
  serviceName: <JOB_NAME>-<BRANCH>
  replicas: 1
  selector:
    matchLabels:
      app: <JOB_NAME>-<BRANCH>
  template:
    metadata:
      labels:
        app: <JOB_NAME>-<BRANCH>
    spec:
      containers:
      - name: <JOB_NAME>-<BRANCH>
        image: 133841643524.dkr.ecr.us-east-2.amazonaws.com/<JOB_NAME>:<BRANCH>-<BUILD_NUMBER>
        env:
        - name: SERVER_ADDR
          value: <SERVER_ADDR>
        - name: STATIC_PATH
          value: /data/uploader
        ports:
        - containerPort: 80
        volumeMounts:
        - name: static-volume
          mountPath: /data
      imagePullSecrets:
      - name: default-aws-ecr-us-east-2
  volumeClaimTemplates:
  - metadata:
      name: static-volume
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: <JOB_NAME>-<BRANCH>
spec:
  ports:
  - name: http
    targetPort: 80
    port: 80
  selector:
    app: <JOB_NAME>-<BRANCH>

---
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  name: <JOB_NAME>-<BRANCH>
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/rule-type: PathPrefixStrip
spec:
  rules:
  - host: <HOST>
    http:
      paths:
      - path: /<JOB_NAME>
        backend:
          serviceName: <JOB_NAME>-<BRANCH>
          servicePort: http
